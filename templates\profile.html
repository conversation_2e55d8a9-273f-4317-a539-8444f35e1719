<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人主页</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body class="bg-light">
    <div class="container">
        <!-- 顶部标题 -->
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mt-4 mb-4">个人中心</h1>
            </div>
        </div>

        <!-- 个人信息卡片 -->
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card shadow-sm">
                    <div class="card-body text-center">
                        <!-- 头像图片 -->
                        <div class="mb-3">
                            <img src="{{ url_for('static', filename='images/road.jpg') }}"
                                 alt="用户头像"
                                 class="rounded-circle profile-avatar">
                        </div>

                        <!-- 用户名 -->
                        <h3 class="card-title mb-3" id="username">加载中...</h3>

                        <!-- 个人简介区域 -->
                        <div class="mb-3">
                            <div id="bioDisplay" class="text-muted">
                                <p id="bioText">加载中...</p>
                            </div>
                            <div id="bioEdit" class="d-none">
                                <textarea class="form-control" id="bioTextarea" rows="4" placeholder="请输入个人简介..."></textarea>
                            </div>
                        </div>

                        <!-- 编辑/保存按钮 -->
                        <div class="mb-3">
                            <button type="button" class="btn btn-primary" id="editBtn" onclick="toggleEdit()">
                                编辑
                            </button>
                            <button type="button" class="btn btn-success d-none" id="saveBtn" onclick="saveBio()">
                                <span id="saveBtnText">保存</span>
                                <span id="saveSpinner" class="spinner-border spinner-border-sm d-none" role="status"></span>
                            </button>
                            <button type="button" class="btn btn-secondary d-none" id="cancelBtn" onclick="cancelEdit()">
                                取消
                            </button>
                        </div>

                        <!-- 退出登录按钮 -->
                        <div class="mt-4">
                            <button type="button" class="btn btn-outline-danger" onclick="logout()">
                                退出登录
                            </button>
                        </div>

                        <!-- 消息提示区域 -->
                        <div id="alert-container" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let isEditing = false;
        let currentUser = null;

        // 页面加载时检查登录状态并获取用户信息
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/';
                return;
            }

            loadUserInfo();
        });

        // 加载用户信息
        async function loadUserInfo() {
            const token = localStorage.getItem('token');

            try {
                const response = await fetch('/api/user', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    currentUser = data.user;
                    updateUI();
                } else {
                    // Token可能已过期，跳转到登录页
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    window.location.href = '/';
                }
            } catch (error) {
                showAlert('获取用户信息失败', 'danger');
            }
        }

        // 更新UI显示
        function updateUI() {
            document.getElementById('username').textContent = currentUser.username;
            document.getElementById('bioText').textContent = currentUser.bio || '暂无个人简介';
            document.getElementById('bioTextarea').value = currentUser.bio || '';
        }

        // 切换编辑模式
        function toggleEdit() {
            isEditing = true;
            document.getElementById('bioDisplay').classList.add('d-none');
            document.getElementById('bioEdit').classList.remove('d-none');
            document.getElementById('editBtn').classList.add('d-none');
            document.getElementById('saveBtn').classList.remove('d-none');
            document.getElementById('cancelBtn').classList.remove('d-none');
            document.getElementById('bioTextarea').focus();
        }

        // 取消编辑
        function cancelEdit() {
            isEditing = false;
            document.getElementById('bioDisplay').classList.remove('d-none');
            document.getElementById('bioEdit').classList.add('d-none');
            document.getElementById('editBtn').classList.remove('d-none');
            document.getElementById('saveBtn').classList.add('d-none');
            document.getElementById('cancelBtn').classList.add('d-none');
            // 恢复原始值
            document.getElementById('bioTextarea').value = currentUser.bio || '';
        }

        // 保存个人简介
        async function saveBio() {
            const token = localStorage.getItem('token');
            const newBio = document.getElementById('bioTextarea').value;
            const saveBtn = document.getElementById('saveBtn');
            const saveBtnText = document.getElementById('saveBtnText');
            const saveSpinner = document.getElementById('saveSpinner');

            // 显示加载状态
            saveBtn.disabled = true;
            saveBtnText.textContent = '保存中...';
            saveSpinner.classList.remove('d-none');

            try {
                const response = await fetch('/api/update-bio', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ bio: newBio })
                });

                const data = await response.json();

                if (data.success) {
                    currentUser.bio = newBio;
                    updateUI();
                    cancelEdit();
                    showAlert('个人简介更新成功', 'success');
                } else {
                    showAlert(data.error || '更新失败', 'danger');
                }
            } catch (error) {
                showAlert('网络错误，请稍后重试', 'danger');
            } finally {
                // 恢复按钮状态
                saveBtn.disabled = false;
                saveBtnText.textContent = '保存';
                saveSpinner.classList.add('d-none');
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.href = '/';
        }

        // 显示提示消息
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            alertContainer.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // 3秒后自动隐藏
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
