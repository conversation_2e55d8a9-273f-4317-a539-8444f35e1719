/* 自定义样式 */

/* 头像样式 */
.profile-avatar {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border: 3px solid #dee2e6;
}

/* 登录页面样式 */
.card {
    border: none;
    border-radius: 10px;
}

.card-body {
    padding: 2rem;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
}

/* 表单样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.75rem;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 个人简介文本区域 */
#bioTextarea {
    resize: vertical;
    min-height: 100px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .profile-avatar {
        width: 100px;
        height: 100px;
    }
    
    .card-body {
        padding: 1.5rem;
    }
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 提示信息样式 */
.alert {
    border-radius: 6px;
    border: none;
}

/* 个人简介显示区域 */
#bioDisplay p {
    margin-bottom: 0;
    min-height: 1.5rem;
    line-height: 1.5;
}

/* 空状态样式 */
.text-muted {
    font-style: italic;
}
