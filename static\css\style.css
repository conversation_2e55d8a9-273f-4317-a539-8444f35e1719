/* 自定义样式 */

/* 个人信息卡片容器 */
.profile-card-container {
    width: 300px;
    margin: 0 auto;
}

/* 个人信息卡片样式 */
.profile-card {
    border-radius: 15px;
    border: none;
    width: 100%;
}

/* 头像容器 */
.profile-avatar-container {
    width: 100%;
    height: 300px;
    overflow: hidden;
    border-radius: 15px 15px 0 0;
}

/* 头像样式 - 方形 */
.profile-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* 个人信息区域 */
.profile-section {
    padding: 20px;
    text-align: center;
}

/* 用户名样式 */
.profile-username {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin: 0;
}

/* 个人简介样式 */
.profile-bio {
    font-size: 1rem;
    color: #666;
    line-height: 1.5;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

/* 编辑按钮样式 */
.profile-btn {
    width: 100%;
    padding: 12px;
    font-size: 1rem;
    border-radius: 8px;
}

/* 编辑文本框样式 */
.bio-textarea {
    width: 100%;
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 12px;
    font-size: 1rem;
    resize: vertical;
    min-height: 100px;
}

/* 登录页面样式 */
.card {
    border: none;
    border-radius: 10px;
}

.card-body {
    padding: 2rem;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
}

/* 表单样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.75rem;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 个人简介文本区域 */
#bioTextarea {
    resize: vertical;
    min-height: 100px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .profile-card-container {
        width: 280px;
    }

    .profile-avatar-container {
        height: 280px;
    }

    .profile-section {
        padding: 15px;
    }

    .profile-username {
        font-size: 1.3rem;
    }

    .profile-bio {
        font-size: 0.9rem;
        min-height: 50px;
    }
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 提示信息样式 */
.alert {
    border-radius: 6px;
    border: none;
}

/* 空状态样式 */
.profile-bio.text-muted {
    font-style: italic;
    color: #999 !important;
}
